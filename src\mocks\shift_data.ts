import type { RawShift, ShiftDefinition } from "../interfaces/calendar"

export const shiftDefinitions: { [key: string]: ShiftDefinition } = {
  A1: { name: "Day Shift", start_time: "07:00", end_time: "15:00", type: "Day" },
  A2: { name: "Day Shift 1", start_time: "06:00", end_time: "14:00", type: "Day" },
  B1: { name: "Night Shift", start_time: "21:00", end_time: "05:00", type: "Night" },
  B2: { name: "Day Shift 2", start_time: "14:00", end_time: "22:00", type: "Day" },
  B4: { name: "Night Shift 2", start_time: "22:00", end_time: "06:00", type: "Night" },
  A5: { name: "Day Shift", start_time: "06:00", end_time: "18:00", type: "Day" },
  B5: { name: "Pre-Overtime Night Shift", start_time: "18:00", end_time: "06:00", type: "Night" },
  CAA1: { name: "Day Shift", start_time: "07:00", end_time: "19:00", type: "Day" },
  CAB1: { name: "Night Shift", start_time: "19:00", end_time: "07:00", type: "Night" },
  A6: { name: "New Day Shift", start_time: "07:00", end_time: "15:10", type: "Day" },
  A7: { name: "New Day Shift 1", start_time: "06:00", end_time: "14:10", type: "Day" },
  A9: { name: "New Day Shift", start_time: "10:00", end_time: "18:10", type: "Day" },
  B3: { name: "New Night Shift", start_time: "21:00", end_time: "05:10", type: "Night" },
  B7: { name: "New Day Shift 2", start_time: "14:00", end_time: "22:10", type: "Day" },
  B9: { name: "New Night Shift 2", start_time: "22:00", end_time: "06:10", type: "Night" },
  BB: { name: "SMT Night Shift", start_time: "19:00", end_time: "03:10", type: "Night" },
  CM1: { name: "CM Day Shift 1", start_time: "07:30", end_time: "15:40", type: "CM" },
  CM2: { name: "CM Day Shift 2", start_time: "08:30", end_time: "16:40", type: "CM" },
  CM3: { name: "CM Day Shift 3", start_time: "09:50", end_time: "18:00", type: "CM" },
  CMA7: { name: "CM Day Shift A7", start_time: "06:00", end_time: "14:10", type: "CM" },
  HA: { name: "Direct Admin Shift", start_time: "08:00", end_time: "16:40", type: "Admin" },
  HO: { name: "Indirect Admin Shift", start_time: "08:00", end_time: "16:50", type: "Admin" },
  HS: { name: "SAS Admin Shift", start_time: "06:45", end_time: "19:25", type: "Admin" },
  TSHC1: { name: "Direct Maternity Early Leave", start_time: "08:00", end_time: "15:40", type: "Maternity" },
  TSHC2: { name: "Direct Maternity Late Arrival", start_time: "09:00", end_time: "16:40", type: "Maternity" },
  TSHO1: { name: "Indirect Maternity Early Leave", start_time: "08:00", end_time: "15:50", type: "Maternity" },
  TSHO2: { name: "Indirect Maternity Late Arrival", start_time: "09:00", end_time: "16:50", type: "Maternity" },
}

export const scheduleData: { [key: string]: RawShift[] } = {
  "2025-08-7": [{ code: "A9" }],
  "2025-08-1": [{ code: "CM2" }],
  "2025-08-3": [{ code: "HS" }],
  "2025-08-01": [{ code: "A1" }],
  "2025-08-22": [{ code: "B1" }],
  "2025-08-03": [{ code: "A5" }],
  "2025-08-04": [{ code: "B5" }],
  "2025-08-05": [{ code: "CM3" }],
  "2025-08-06": [{ code: "HO" }],
  "2025-08-27": [{ code: "TSHO1" }],
  "2025-08-08": [{ code: "A7" }],
  "2025-08-09": [{ code: "BB" }],
  "2025-08-10": [{ code: "CMA7" }],
  "2025-08-11": [{ code: "TSHC2" }],
  "2025-08-12": [{ code: "TSHO2" }],
  "2025-08-13": [{ code: "A1" }],
  "2025-08-15": [{ code: "A2" }],
  "2025-08-20": [{ code: "A6" }],
}
