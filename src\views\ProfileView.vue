<script setup lang="ts">
import ShowInfomation from '@/components/profile/ShowInfomation.vue';
import Button from '@/components/ui/button/Button.vue';
import { LanguageSelector } from '@/components/ui/language-selector';
import { useLocale } from '@/composables/useLocale';
import { languages } from '@/constants/language-list';
import { EDIT_PROFILE, LOGIN, SCAN_ID_CARD } from '@/constants/routes';
import { useUserStore } from '@/stores/user';
import { getEmployeeName } from '@/utils/employee-name';
import { navigateToLocalizedRoute } from '@/utils/localized-navigation';
import { IonPage } from '@ionic/vue';
import { Edit, LogOut, ScanLine } from 'lucide-vue-next';
import { NAvatar } from 'naive-ui';
import { computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { toast } from 'vue-sonner';

const { t } = useI18n();
const userStore = useUserStore();
const router = useRouter();
const employeeName = getEmployeeName();
const { locale } = useLocale();

onMounted(async () => {
  try {
    await userStore.getCurrentUser();
  } catch (error) {
    toast.error(`Failed to fetch user profile: ${String(error)}`);
  }
});

const hasAvatar = computed(() => !!userStore.user?.profile_image);

const handleLogout = async () => {
  await userStore.logout();
  toast.success('Logged out successfully!');
  router.push(LOGIN);
};

const goToEditProfile = () => {
  navigateToLocalizedRoute(EDIT_PROFILE, locale.value);
};

const goToScanIdCard = () => {
  navigateToLocalizedRoute(SCAN_ID_CARD, locale.value);
};
</script>

<template>
  <ion-page>
    <div class="scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4">
      <LanguageSelector :languages="languages" :show-name="true" custom-class="w-[175px] ml-auto mb-5" />
      <div class="flex flex-col items-center w-full" style="gap: 16px 20px">
        <NAvatar v-if="hasAvatar" round size="large" :src="userStore.user?.profile_image" />
        <NAvatar v-else round size="large">
          {{ userStore.userInitials }}
        </NAvatar>
        <div class="flex w-full items-center justify-center gap-x-5">
          <span class="text-2xl font-bold">{{ employeeName }}</span>
          <button class="cursor-pointer text-gray-500" @click="goToEditProfile">
            <Edit class="size-5" stroke-width="2" />
          </button>
          <button class="cursor-pointer text-gray-500" @click="goToScanIdCard">
            <ScanLine class="size-5" stroke-width="2" />
          </button>
        </div>

        <ShowInfomation />

        <Button type="button" variant="destructive" size="lg" @click="handleLogout" class="mt-4 w-full text-lg">
          <LogOut class="size-5" stroke-width="2.5" />
          {{ t('common.logout') }}
        </Button>
      </div>
    </div>
  </ion-page>
</template>

<style scoped>
.n-avatar {
  --n-font-size: 20px !important;
  --n-merged-size: 100px !important;
}
</style>
