<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useAttendanceForm } from '@/composables/useAttendanceForm';
import { useDateFormats } from '@/composables/useDateFormats';
import { useProfile } from '@/composables/useProfile';
import { FormMode } from '@/enums';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import {
  NAlert,
  NCheckbox,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
  NTimePicker,
} from 'naive-ui';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  formValue,
  formRef,
  rules,
  alert,
  hasEmployees,
  staffApproveOptions,
  staffOptions,
  workShiftOptions,
  handleSubmit,
} = useAttendanceForm(FormMode.CREATE);
const { user } = useProfile();
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const { pickerConfigs } = useDateFormats();
</script>

<template>
  <ion-page>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4 p-4">
      <n-alert v-if="alert.visible" :title="alert.title" :type="alert.type" closable @close="alert.visible = false"
        bordered class="mb-4 w-full">
        {{ alert.message }}
      </n-alert>

      <n-form ref="formRef" :model="formValue" :rules="rules" class="w-full">
        <n-form-item :label="$t('attendance.register_overtime.additional_day')" path="additional_day" class="w-full"
          required>
          <n-date-picker v-model:formatted-value="formValue.additional_day" type="date"
            :format="pickerConfigs.attendance.date.format" :value-format="pickerConfigs.attendance.date.valueFormat"
            :placeholder="$t('attendance.register_overtime.additional_day_placeholder')
              " clearable class="w-full" />
        </n-form-item>

        <n-checkbox v-if="isNonEmployee" v-model:checked="hasEmployees" size="small"
          :class="cn('w-full', hasEmployees ? 'mb-2' : 'mb-4')">
          {{ t('leaves.new_request.register_for_employee') }}
        </n-checkbox>

        <n-form-item v-if="isNonEmployee && hasEmployees" :label="t('leaves.new_request.staff')" path="staff">
          <n-select filterable multiple v-model:value="formValue.employees" :options="staffOptions"
            :placeholder="t('leaves.new_request.staff_placeholder')" />
        </n-form-item>

        <n-form-item :label="$t('attendance.register_overtime.work_shift')" class="w-full" path="shift_id">
          <n-select v-model:value="formValue.shift_id" filterable :options="workShiftOptions" :placeholder="t('attendance.register_overtime.work_shift_placeholder')
            " />
        </n-form-item>
        <div class="grid grid-cols-2 gap-4">
          <n-form-item :label="$t('attendance.register_overtime.time_in')" path="time_in" class="w-full"
            label-style="font-semibold text-base">
            <n-time-picker v-model:value="formValue.time_in" :format="pickerConfigs.attendance.time.format"
              :placeholder="$t('attendance.register_overtime.time_in_placeholder')
                " clearable class="w-full" />
          </n-form-item>

          <n-form-item :label="$t('attendance.register_overtime.time_out')" path="time_out" class="w-full">
            <n-time-picker v-model:value="formValue.time_out" :format="pickerConfigs.attendance.time.format"
              :placeholder="$t('attendance.register_overtime.time_out_placeholder')
                " clearable class="w-full" />
          </n-form-item>
        </div>

        <n-form-item label-class="font-semibold text-base" :label="$t('attendance.register_overtime.timekeeping_value')"
          path="timekeeping_value" class="w-full">
          <n-input v-model:value="formValue.timekeeping_value" placeholder="" class="w-full" disabled />
        </n-form-item>

        <n-form-item :label="t('attendance.register_overtime.approver')" path="approver_id">
          <n-select v-model:value="formValue.approver_id" filterable :options="staffApproveOptions" :placeholder="t('attendance.register_overtime.approver_placeholder')
            " />
        </n-form-item>

        <n-form-item :label="$t('attendance.register_overtime.reason')" path="reason" class="w-full">
          <n-input v-model:value="formValue.reason" type="textarea"
            :placeholder="$t('attendance.register_overtime.reason_placeholder')" :autosize="{ minRows: 3, maxRows: 7 }"
            class="!h-[125px] w-full" />
        </n-form-item>

        <Button size="lg" @click="handleSubmit" class="w-full text-base">
          {{ t('common.submit') }}
        </Button>
      </n-form>
    </div>
  </ion-page>
</template>
