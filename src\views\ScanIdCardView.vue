<template>
  <IonPage>
    <div class="scroll-container h-full gap-y-4 bg-gray-50 p-4">
      <div class="rounded-lg bg-white p-4 shadow-sm">
        <NFormItem :label="t('profile.edit.id_card_front')" class="w-full">
          <div class="flex justify-center items-center ">
            <div class="space-y-3">
              <div v-if="idCardFrontImage" class="relative inline-block">
                <img :src="idCardFrontImage" alt="ID Card Front"
                  class="h-32 w-52 object-cover rounded-lg border border-gray-200 shadow-sm" />
                <Button variant="destructive" size="icon" @click="removeIdCardImage('front')"
                  class="absolute -top-2 -right-2 h-6 w-6 rounded-full">
                  <X class="h-3 w-3" />
                </Button>
              </div>

              <div v-else
                class="flex items-center justify-center w-52 h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors cursor-pointer"
                @click="uploadIdCardFront">
                <div class="text-center">
                  <Upload class="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p class="text-sm text-gray-500">{{ t('profile.edit.upload_id_front') }}</p>
                  <p class="text-xs text-gray-400 mt-1">{{ t('profile.edit.max_file_size') }}</p>
                </div>
              </div>

              <Button v-if="idCardFrontImage" variant="outline" size="sm" @click="uploadIdCardFront" class="mt-2">
                <Camera class="mr-2 h-4 w-4" />
                {{ t('profile.edit.change_image') }}
              </Button>
            </div>
          </div>
        </NFormItem>

        <NFormItem :label="t('profile.edit.id_card_back')" class="w-full">
          <div class="flex justify-center">
            <div class="space-y-3">
              <div v-if="idCardBackImage" class="relative inline-block">
                <img :src="idCardBackImage" alt="ID Card Back"
                  class="h-32 w-52 object-cover rounded-lg border border-gray-200 shadow-sm" />
                <Button variant="destructive" size="icon" @click="removeIdCardImage('back')"
                  class="absolute -top-2 -right-2 h-6 w-6 rounded-full">
                  <X class="h-3 w-3" />
                </Button>
              </div>

              <div v-else
                class="flex items-center justify-center w-52 h-32 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 transition-colors cursor-pointer"
                @click="uploadIdCardBack">
                <div class="text-center">
                  <Upload class="mx-auto h-8 w-8 text-gray-400 mb-2" />
                  <p class="text-sm text-gray-500">{{ t('profile.edit.upload_id_back') }}</p>
                  <p class="text-xs text-gray-400 mt-1">{{ t('profile.edit.max_file_size') }}</p>
                </div>
              </div>

              <Button v-if="idCardBackImage" variant="outline" size="sm" @click="uploadIdCardBack" class="mt-2">
                <Camera class="mr-2 h-4 w-4" />
                {{ t('profile.edit.change_image') }}
              </Button>
            </div>
          </div>
        </NFormItem>

        <div class="p-3 bg-blue-50 rounded-lg">
          <div class="flex items-start">
            <Image class="h-5 w-5 text-blue-500 mt-0.5 mr-2 flex-shrink-0" />
            <div class="text-sm text-blue-700">
              <p class="font-medium mb-1">{{ t('profile.edit.id_card_note_title') }}</p>
              <ul class="list-disc list-inside space-y-1 text-xs">
                <li>{{ t('profile.edit.id_card_note_1') }}</li>
                <li>{{ t('profile.edit.id_card_note_2') }}</li>
                <li>{{ t('profile.edit.id_card_note_3') }}</li>
              </ul>
            </div>
          </div>
        </div>

        <!-- Extract Button -->
        <div class="mt-6 space-y-4">
          <Button @click="handleExtractInfo" :disabled="!canExtract" :loading="isExtracting" class="w-full" size="lg">
            <Scan class="mr-2 h-5 w-5" />
            {{ isExtracting ? t('profile.scan_id_card.extracting') : t('profile.scan_id_card.extract_info') }}
          </Button>

          <!-- Extracted Data Display -->
          <div v-if="extractedData" class="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="font-semibold text-green-800 flex items-center">
                <FileText class="mr-2 h-5 w-5" />
                {{ t('profile.scan_id_card.extracted_info') }}
              </h4>
              <Button @click="applyExtractedData" size="sm" variant="outline">
                {{ t('profile.scan_id_card.apply_data') }}
              </Button>
            </div>
            <div class="grid grid-cols-1 gap-3 text-sm">
              <div class="flex justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.full_name') }}:</span>
                <span class="text-gray-900">{{ extractedData.full_name }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.identification_number') }}:</span>
                <span class="text-gray-900">{{ extractedData.citizen_id }}</span>
              </div>

              <div class="flex justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.birthday') }}:</span>
                <span class="text-gray-900">{{ extractedData.dob }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.gender') }}:</span>
                <span class="text-gray-900">{{ extractedData.sex }}</span>
              </div>
              <div class="flex flex-col justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.personal_identification') }}:</span>
                <span class="text-gray-900">{{ extractedData.personal_identification }}</span>
              </div>
              <div class="flex flex-col justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.current_address') }}:</span>
                <span class="text-gray-900">{{ extractedData.add_str }}</span>
              </div>
              <div class="flex flex-col justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.place_of_issue') }}:</span>
                <span class="text-gray-900">{{ extractedData.director_general_police_department }}</span>
              </div>
              <div class="flex justify-between">
                <span class="font-medium text-gray-600">{{ t('profile.edit.issue_date') }}:</span>
                <span class="text-gray-900">{{ extractedData.date_issue }}</span>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  </IonPage>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';
import { NFormItem } from 'naive-ui';
import { IonPage } from '@ionic/vue';
import { Button } from '@/components/ui/button';
import { Camera, Upload, X, Image, Scan, FileText } from 'lucide-vue-next';
import { extractIdentifi } from '@/services/extract.service';
import type { CitizenInfo } from '@/interfaces/staff';

const { t } = useI18n();

const idCardFrontImage = ref<string | null>(null);
const idCardBackImage = ref<string | null>(null);

const frontFile = ref<File | null>(null);
const backFile = ref<File | null>(null);

const isExtracting = ref(false);
const extractedData = ref<CitizenInfo["info"] | null>(null);


const canExtract = computed(() => {
  return frontFile.value && backFile.value && !isExtracting.value;
});
const uploadIdCardFront = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement | null;
    const file = target?.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(t('profile.edit.file_too_large'));
        return;
      }

      if (!file.type.startsWith('image/')) {
        toast.error(t('profile.edit.invalid_file_type'));
        return;
      }

      frontFile.value = file;

      const reader = new FileReader();
      reader.onload = (e) => {
        idCardFrontImage.value = e.target?.result as string;
        toast.success(t('profile.edit.id_card_front_uploaded'));
      };
      reader.readAsDataURL(file);
    }
  };
  input.click();
};

const uploadIdCardBack = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement | null;
    const file = target?.files?.[0];
    if (file) {
      if (file.size > 5 * 1024 * 1024) {
        toast.error(t('profile.edit.file_too_large'));
        return;
      }

      if (!file.type.startsWith('image/')) {
        toast.error(t('profile.edit.invalid_file_type'));
        return;
      }

      backFile.value = file;

      const reader = new FileReader();
      reader.onload = (e) => {
        idCardBackImage.value = e.target?.result as string;
        toast.success(t('profile.edit.id_card_back_uploaded'));
      };
      reader.readAsDataURL(file);
    }
  };
  input.click();
};

const removeIdCardImage = (type: 'front' | 'back') => {
  if (type === 'front') {
    idCardFrontImage.value = null;
    frontFile.value = null;
    toast.success(t('profile.edit.id_card_front_removed'));
  } else {
    idCardBackImage.value = null;
    backFile.value = null;
    toast.success(t('profile.edit.id_card_back_removed'));
  }

  extractedData.value = null;
};

const handleExtractInfo = async () => {
  if (!frontFile.value || !backFile.value) {
    toast.error(t('profile.scan_id_card.missing_images'));
    return;
  }

  isExtracting.value = true;

  try {
    toast.info(t('profile.scan_id_card.extracting'));

    const response = await extractIdentifi(frontFile.value, backFile.value);

    console.warn('Extract response:', response); // Debug log

    if (response && response.status_code === 200) {
      extractedData.value = response.info;
      toast.success(t('profile.scan_id_card.extract_success'));
    } else {
      throw new Error(response.message || 'Extract failed');
    }
  } catch (error) {
    console.error('Extract error:', error);

    if (error instanceof Error) {
      toast.error(`${t('profile.scan_id_card.extract_error')}: ${error.message}`);
    } else {
      toast.error(t('profile.scan_id_card.extract_error'));
    }
  } finally {
    isExtracting.value = false;
  }
};

const applyExtractedData = () => {
  if (!extractedData.value) return;
  toast.success(t('profile.scan_id_card.data_applied'));
};
</script>
<style>
.n-form-item {
  display: block !important;
}
</style>
