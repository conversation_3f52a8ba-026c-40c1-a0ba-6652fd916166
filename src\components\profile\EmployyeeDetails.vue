<script setup lang="ts">
import {
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useLocale } from '@/composables/useLocale';
import { useUserStore } from '@/stores/user';
import { formatDateEmployee } from '@/utils/format';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { locale } = useLocale();
const userStore = useUserStore();

const detailsList = computed(() => [
  {
    label: t('profile.employee_number'),
    content: userStore.user?.staff_identifi || '-',
  },
  {
    label: t('profile.identification'),
    content: userStore.user?.identification || '-',
  },
  {
    label: t('profile.full_name'),
    content:
      userStore.user?.full_name_vi
        ? userStore.user.full_name_vi
        : '-',
  },
  {
    label: t('profile.gender'),
    content: userStore.user?.sex || '-',
  },
  {
    label: t('profile.religion'),
    content: userStore.user?.religion || '-',
  },
  {
    label: t('profile.employee_number'),
    content: userStore.user?.staff_identifi || '-',
  },
  {
    label: t('profile.day_of_birth'),
    content: formatDateEmployee(userStore.user?.birthday, locale.value) || '-',
  },
  {
    label: t('profile.day_of_joining'),
    content: formatDateEmployee(userStore.user?.start_work_date, locale.value) || '-',
  },
]);
</script>

<template>
  <DrawerContent>
    <DrawerHeader>
      <DrawerTitle class="text-center">{{
        t('profile.employee_details')
      }}</DrawerTitle>
      <DrawerDescription class="hidden" />
    </DrawerHeader>
    <Separator />
    <div class="flex w-full flex-col items-center justify-center gap-4 p-4">
      <div v-for="(item, index) in detailsList" :key="index" class="flex w-full items-center justify-between gap-x-2">
        <div class="text-sm text-gray-600">{{ item.label }}</div>
        <div class="font-medium text-gray-900">{{ item.content }}</div>
      </div>
    </div>
  </DrawerContent>
</template>
