import * as ROUTES from '@/constants/routes';

import { RoleName } from '@/enums/role';
import type { RouteRecordRaw } from 'vue-router';
import { SUPPORTED_LOCALES } from '@/constants/locales';
import { checkPermission } from '@/helpers/permission-helper';

export const baseRoutes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: { name: 'home' },
  },
  {
    path: ROUTES.LOGIN,
    name: 'login',
    component: () => import('@/views/LoginView.vue'),
    meta: {
      layout: 'auth',
      title: 'login.title',
      requiresAuth: false,
      excludeFromHistory: true,
    },
  },
  {
    path: ROUTES.HOME,
    name: 'home',
    component: () => import('@/views/HomeView.vue'),
    meta: {
      layout: 'root',
      title: 'home.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.ATTENDANCE,
    name: 'attendance',
    component: () => import('@/views/AttendanceView.vue'),
    meta: {
      layout: 'root',
      title: 'attendance.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.ATTENDANCE_HISTORY,
    name: 'attendance-history',
    component: () => import('@/views/AttendanceHistoryView.vue'),
    meta: {
      layout: 'action',
      title: 'attendance.history.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.LEAVES,
    name: 'leaves',
    component: () => import('@/views/LeaveView.vue'),
    meta: {
      layout: 'root',
      title: 'leaves.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.LEAVES_HISTORY,
    name: 'leaves-history',
    component: () => import('@/views/LeaveHistory.vue'),
    meta: {
      layout: 'action',
      title: 'leaves.history.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.EXPENSES,
    name: 'expenses',
    component: () => import('@/views/ExpenseView.vue'),
    meta: {
      layout: 'root',
      title: 'expense.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.DASHBOARD_SALARY_SLIPS,
    name: 'salary-slips',
    component: () => import('@/views/SalaryView.vue'),
    meta: {
      layout: 'root',
      title: 'salary.slips',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.NOTIFICATION,
    name: 'notification',
    component: () => import('@/views/NotificationView.vue'),
    meta: {
      layout: 'action',
      title: 'notification.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.PROFILE,
    name: 'profile',
    component: () => import('@/views/ProfileView.vue'),
    meta: {
      layout: 'action',
      title: 'profile.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.EDIT_PROFILE,
    name: 'edit-profile',
    component: () => import('@/views/EditProfileView.vue'),
    meta: {
      layout: 'action',
      title: 'profile.edit.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.SCAN_ID_CARD,
    name: 'scan-id-card',
    component: () => import('@/views/ScanIdCardView.vue'),
    meta: {
      layout: 'action',
      title: 'profile.scan_id_card.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.UPDATE_OVERTIME,
    name: 'update-overtime',
    component: () => import('@/views/UpdateOvertimeView.vue'),
    meta: {
      layout: 'action',
      title: 'attendance.register_overtime.edit_title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.REGISTER_OVERTIME,
    name: 'register-overtime',
    component: () => import('@/views/RegisterOvertimeView.vue'),
    meta: {
      layout: 'action',
      title: 'attendance.register_overtime.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.OVERTIME_HISTORY,
    name: 'overtime-history',
    component: () => import('@/views/OvertimeHistoryView.vue'),
    meta: {
      layout: 'action',
      title: 'attendance.overtime.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.NEW_LEAVE_APPLICATIONS,
    name: 'new-leaves',
    component: () => import('@/views/LeaveMutationRequest.vue'),
    meta: {
      layout: 'action',
      title: 'leaves.new_request.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.EDIT_LEAVE_APPLICATIONS,
    name: 'edit-leaves',
    component: () => import('@/views/LeaveUpdateRequest.vue'),
    meta: {
      layout: 'action',
      title: 'leaves.edit_request.title',
      requiresAuth: true,
    },
  },
  {
    path: ROUTES.MANAGEMENT_REQUESTS,
    name: 'management-leaves',
    component: () => import('@/views/ManagementRequestView.vue'),
    meta: {
      layout: 'action',
      title: 'management.requests.title',
      requiresAuth: true,
      role: RoleName.MANAGER,
    },
    beforeEnter: checkPermission,
  },
  {
    path: ':pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/NotFoundView.vue'),
    meta: {
      layout: 'root',
      title: 'common.not_found.title',
      excludeFromHistory: true,
    },
  },
];

export function createLocalizedRoutes(): Array<RouteRecordRaw> {
  const localizedRoutes: Array<RouteRecordRaw> = [];

  localizedRoutes.push({
    path: '/',
    redirect: `/${SUPPORTED_LOCALES[0]}`,
  });

  SUPPORTED_LOCALES.forEach((locale) => {
    localizedRoutes.push({
      path: `/${locale}`,
      redirect: `/${locale}${ROUTES.HOME}`,
    });

    const routes = baseRoutes
      .map((route) => {
        if (route.path === '/') return null;

        const localizedRoute: RouteRecordRaw = {
          ...route,
          path: `/${locale}${route.path}`,
          name: route.name ? `${locale}-${String(route.name)}` : undefined,
          meta: {
            ...route.meta,
            locale,
          },
        };

        return localizedRoute;
      })
      .filter(Boolean) as RouteRecordRaw[];

    localizedRoutes.push(...routes);
  });

  localizedRoutes.push({
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/NotFoundView.vue'),
    beforeEnter: (to, from, next) => {
      const path = to.path.startsWith('/') ? to.path.slice(1) : to.path;

      const startsWithLocale = SUPPORTED_LOCALES.some(
        (locale) => path.startsWith(`${locale}/`) || path === locale,
      );

      if (startsWithLocale) {
        next();
      } else {
        if (path === '' || path === '/') {
          next(`/${SUPPORTED_LOCALES[0]}`);
        } else {
          next(`/${SUPPORTED_LOCALES[0]}/${path}`);
        }
      }
    },
  });

  return localizedRoutes;
}
