import { computed, ref } from 'vue';

import type { AttendanceDay } from '@/interfaces/attendance';
import { getDetailsAttandanceLogs } from '@/services/attendance.service';
import { useProfile } from './useProfile';
import { useQuery } from '@tanstack/vue-query';

export interface UserWorkShift {
  shift_id: number;
  shift_code: string;
  time_start: string;
  time_end: string;
  description: string;
}

export const useWorkScheduleCheck = () => {
  const { user } = useProfile();
  const selectedDate = ref<string | null>(null);

  const workScheduleQuery = useQuery({
    queryKey: ['work-schedule-check', selectedDate, user?.value?.id],
    queryFn: async () => {
      if (!selectedDate.value || !user?.value?.id) {
        return null;
      }

      const filters = {
        date_from: selectedDate.value,
        date_to: selectedDate.value,
        staff_id: user.value.id
      };

      const response = await getDetailsAttandanceLogs(filters);
      return response.data;
    },
    enabled: computed(() => !!selectedDate.value && !!user?.value?.id),
  });

  const hasWorkShift = computed(() => {
    const data = workScheduleQuery.data.value;
    if (!data || !data.data || data.data.length === 0) {
      return false;
    }

    const dayData = data.data[0] as AttendanceDay;
    return dayData?.shifts?.assigned_shifts && dayData.shifts.assigned_shifts.length > 0;
  });

  const currentWorkShift = computed((): UserWorkShift | null => {
    const data = workScheduleQuery.data.value;
    if (!data || !data.data || data.data.length === 0) {
      return null;
    }

    const dayData = data.data[0] as AttendanceDay;
    const assignedShifts = dayData?.shifts?.assigned_shifts;

    if (!assignedShifts || assignedShifts.length === 0) {
      return null;
    }

    const shift = assignedShifts[0];
    return {
      shift_id: shift.id,
      shift_code: shift.shift_code,
      time_start: shift.time_in,
      time_end: shift.time_out,
      description: shift.shifts_detail || ''
    };
  });

  const setSelectedDate = (date: string | null) => {
    selectedDate.value = date;
  };

  // Reset data
  const reset = () => {
    selectedDate.value = null;
  };

  return {
    selectedDate: computed(() => selectedDate.value),
    hasWorkShift,
    currentWorkShift,
    isLoading: computed(() => workScheduleQuery.isLoading.value),
    isError: computed(() => workScheduleQuery.isError.value),
    error: computed(() => workScheduleQuery.error.value),
    setSelectedDate,
    reset,
    refetch: workScheduleQuery.refetch
  };
};
