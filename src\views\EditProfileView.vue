<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useDateFormats } from '@/composables/useDateFormats';
import { useHeaderActionsStore } from '@/stores/header-actions';
import { useUserStore } from '@/stores/user';
// import { parseDate } from '@/utils/format';
import { IonPage } from '@ionic/vue';
import { Briefcase, Camera, CreditCard, Phone, User } from 'lucide-vue-next';
import {
  NAutoComplete,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NSelect,
} from 'naive-ui';
import { h, onMounted, onUnmounted, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { toast } from 'vue-sonner';

const { t } = useI18n();
const userStore = useUserStore();
const headerActionsStore = useHeaderActionsStore();
const { dateFormats } = useDateFormats();

// Form data
const formData = ref({
  // Personal Information
  fullname_vn: '',
  fullname_en: '',
  birthday: null as number | null,
  birth_place: '',
  sex: '',
  home_town: '',
  current_address: '',
  religion: '',
  nation: '',
  literacy: '',
  major: '',
  marital_status: '',

  // Contact Information
  personal_email: '',
  company_email: '',
  phone_number: '',

  // Work Information
  staff_identifi: '',
  position_name: '',
  department_name: '',
  workplace_name: '',
  workplace_address: '',
  start_work_date: null as number | null,
  vehicle_plate: '',

  // Identity Information
  identification_number: '',
  place_of_issue: '',
  issue_date: null as number | null,
  personal_tax_code: '',
  social_security_no: '',

  // Banking Information
  account_number: '',
  name_account: '',
  issue_bank: '',
});

const genderOptions = ref([
  { value: 'male', label: t('profile.edit.male') },
  { value: 'female', label: t('profile.edit.female') },
  { value: 'other', label: t('profile.edit.other') },
]);

const educationOptions = ref([
  { value: 'high_school', label: t('profile.edit.high_school') },
  { value: 'bachelor', label: t('profile.edit.bachelor') },
  { value: 'master', label: t('profile.edit.master') },
  { value: 'phd', label: t('profile.edit.phd') },
  { value: 'other', label: t('profile.edit.other') },
]);

const maritalStatusOptions = ref([
  { value: 'single', label: t('profile.edit.single') },
  { value: 'married', label: t('profile.edit.married') },
  { value: 'divorced', label: t('profile.edit.divorced') },
]);

// Load user data into form
const userInfomation = () => {
  if (userStore.user) {
    const user = userStore.user;
    // Personal Information
    formData.value.fullname_vn = user.fullname_vn || '';
    formData.value.fullname_en = user.fullname_en || '';
    formData.value.birthday = user.birthday ? new Date(user.birthday).getTime() : null;
    formData.value.birth_place = user.birth_place || '';
    formData.value.sex = user.sex || '';
    formData.value.home_town = user.home_town || '';
    formData.value.current_address = user.current_address || '';
    formData.value.religion = user.religion || '';
    formData.value.nation = user.nation || '';
    formData.value.literacy = user.literacy || '';
    formData.value.major = user.major || '';
    formData.value.marital_status = user.marital_status || '';
    // Contact Information
    formData.value.personal_email = user.personal_email || '';
    formData.value.company_email = user.company_email || '';
    formData.value.phone_number = user.phone_number || '';
    // Work Information
    formData.value.staff_identifi = user.staff_identifi || '';
    formData.value.position_name = user.position_name || '';
    formData.value.department_name = user.department_name || '';
    formData.value.workplace_name = user.workplace_name || '';
    formData.value.workplace_address = user.workplace_address || '';
    formData.value.start_work_date = user.start_work_date ? new Date(user.start_work_date).getTime() : null;
    formData.value.vehicle_plate = user.vehicle_plate || '';
    // Identity Information
    formData.value.identification_number = user.identification_number || '';
    formData.value.place_of_issue = user.place_of_issue || '';
    formData.value.issue_date = user.issue_date ? new Date(user.issue_date).getTime() : null;
    formData.value.personal_tax_code = user.personal_tax_code || '';
    formData.value.social_security_no = user.social_security_no || '';
    // Banking Information
    formData.value.account_number = user.account_number || '';
    formData.value.name_account = user.name_account || '';
    formData.value.issue_bank = user.issue_bank || '';
  }
};

onMounted(async () => {
  try {
    await userStore.getCurrentUser();
    userInfomation();
  } catch (error) {
    toast.error(`Failed to fetch user profile: ${String(error)}`);
  }
});

const changeProfileImage = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/*';
  input.onchange = (e) => {
    const target = e.target as HTMLInputElement | null;
    const file = target?.files?.[0];
    if (file) {
      const reader = new FileReader();

      reader.readAsDataURL(file);
    }
  };
  input.click();
};

const handleSubmit = async () => {
  try {
    toast.success(t('profile.edit.save_success'));
  }
  catch (error) {
    toast.error(`Failed to update user profile: ${String(error)}`);
  }
}

onMounted(() => {
  headerActionsStore.set(() =>
    h(
      'button',
      {
        class:
          'p-2 transition-colors hover:bg-gray-100 cursor-pointer rounded-md text-gray-800 font-semibold text-base',
      },
      t('profile.edit.save'),
    ),
  );
});

onUnmounted(() => {
  headerActionsStore.clear();
});
</script>

<template>
  <IonPage>
    <div class="scroll-container flex h-full flex-col items-center gap-y-4 bg-gray-50">
      <NForm label-placement="top" class="w-full space-y-6 p-4">
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <div class="flex flex-col items-center">
            <div class="relative">
              <div class="flex h-24 w-24 items-center justify-center overflow-hidden rounded-full bg-gray-200">
                <img v-if="userStore.user?.profile_image" :src="userStore.user?.profile_image" alt="Profile"
                  class="h-full w-full object-cover" />
                <User v-else class="h-12 w-12 text-gray-400" />
              </div>
              <Button variant="custom" size="icon" @click="changeProfileImage"
                class="absolute right-0 bottom-0 rounded-full bg-gray-500">
                <Camera class="size-4" />
              </Button>
            </div>
            <p class="mt-2 text-sm text-gray-500">
              {{ t('profile.edit.tap_to_change_photo') }}
            </p>
          </div>
        </div>

        <!-- Personal Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <User class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.personal_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">

          <NFormItem :label="t('profile.full_name')">
            <NInput v-model:value="formData.fullname_vn" :placeholder="t('profile.full_name')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birthday')">
            <NDatePicker v-model:value="formData.birthday" class="w-full" :format="dateFormats.date.display"
              :placeholder="dateFormats.date.display" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.birth_place')">
            <NInput v-model:value="formData.birth_place" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.gender')">
            <NSelect v-model:value="formData.sex" :options="genderOptions"
              :placeholder="t('profile.edit.select_gender')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.home_town')">
            <NInput v-model:value="formData.home_town" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.current_address')">
            <NInput v-model:value="formData.current_address" type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.permanent_address')">
            <NInput v-model:value="formData.current_address" type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.religion')">
            <NInput v-model:value="formData.religion" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.nation')">
            <NInput v-model:value="formData.nation" :placeholder="t('profile.edit.select_nation')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.education_level')">
            <NSelect v-model:value="formData.literacy" :options="educationOptions"
              :placeholder="t('profile.edit.select_education_level')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.major')">
            <NInput v-model:value="formData.major" :placeholder="t('profile.edit.major_placeholder')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.marital_status')">
            <NSelect v-model:value="formData.marital_status" :options="maritalStatusOptions"
              :placeholder="t('profile.edit.select_marital_status')" />
          </NFormItem>
        </div>

        <!-- Contact Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <Phone class="mr-2 h-5 w-5" />
          {{ t('profile.edit.contact_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.company_email')" class="mb-3">
            <NInput v-model:value="formData.company_email" disabled
              :placeholder="t('profile.edit.company_email_placeholder')" />
          </NFormItem>

          <NFormItem :label="t('profile.email')" class="mb-3">
            <NAutoComplete v-model:value="formData.personal_email" :placeholder="t('profile.edit.email_placeholder')" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.phone_number')">
            <NInput v-model:value="formData.phone_number" :placeholder="t('profile.edit.phone_number_placeholder')" />
          </NFormItem>
        </div>

        <!-- Work Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <Briefcase class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.work_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.staff_id')" class="mb-3">
            <NInput v-model:value="formData.staff_identifi" placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.staff_id_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.position')">
            <NInput v-model:value="formData.position_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.department')">
            <NInput v-model:value="formData.department_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace')">
            <NInput v-model:value="formData.workplace_name" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.workplace_address')">
            <NInput v-model:value="formData.workplace_address" type="textarea" placeholder="" class="h-[80px]" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.start_work_date')">
            <NDatePicker v-model:value="formData.start_work_date" placeholder="dd/MM/yyy" class="w-full" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.vehicle_plate')">
            <NInput v-model:value="formData.vehicle_plate" :placeholder="t('profile.edit.vehicle_plate_placeholder')" />
          </NFormItem>
        </div>

        <!-- Identity Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <CreditCard class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.identity_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.identification_number')" class="mb-3">
            <NInput v-model:value="formData.identification_number" placeholder="" disabled />
            <template #feedback>
              <p class="mt-1 text-xs text-gray-500">
                {{ t('profile.edit.identification_number_feedback') }}
              </p>
            </template>
          </NFormItem>

          <NFormItem :label="t('profile.edit.place_of_issue')">
            <NInput v-model:value="formData.place_of_issue" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.issue_date')">
            <NDatePicker v-model:value="formData.issue_date" placeholder="dd/MM/yyyy" class="w-full" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.personal_tax_code')">
            <NInput v-model:value="formData.personal_tax_code" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.social_insurance_number')">
            <NInput v-model:value="formData.social_security_no" placeholder="" />
          </NFormItem>
        </div>

        <!-- Banking Information -->
        <h2 class="mb-4 flex items-center text-base font-semibold text-gray-700">
          <CreditCard class="mr-2 h-5 w-5 " />
          {{ t('profile.edit.banking_information') }}
        </h2>
        <div class="rounded-lg bg-white p-4 shadow-sm">
          <NFormItem :label="t('profile.edit.account_number')">
            <NInput v-model:value="formData.account_number" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.account_name')">
            <NInput v-model:value="formData.name_account" placeholder="" />
          </NFormItem>

          <NFormItem :label="t('profile.edit.bank_name')">
            <NInput v-model:value="formData.issue_bank" placeholder="" />
          </NFormItem>
        </div>

        <Button size="lg" type="submit" @click="handleSubmit" class="mb-4 w-full">
          {{ t('profile.edit.save_changes') }}
        </Button>
      </NForm>
    </div>
  </IonPage>
</template>
