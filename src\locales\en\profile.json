{"profile": {"title": "Profile", "language": "Language", "employee_details": "Employee Details", "employee_number": "Employee Number", "identification": "Identification", "full_name": "Full Name", "email": "Email", "personal_email": "Personal Email", "company_email": "Company Email", "gender": "Gender", "religion": "Religion", "home_town": "Home Town", "day_of_birth": "Day of Birth", "day_of_joining": "Day of Joining", "company_information": "Company Information", "department": "Department", "position": "Position", "workplace": "Workplace", "workplace_address": "Workplace Address", "contact_information": "Contact Information", "mobile": "Mobile", "address": "Address", "company": "Company", "salary_information": "Salary Information", "edit": {"title": "Edit Profile", "save": "Save", "save_changes": "Save Changes", "personal_information": "Personal Information", "contact_information": "Contact Information", "work_information": "Work Information", "identity_information": "Identity Information", "banking_information": "Banking Information", "first_name": "First Name", "last_name": "Last Name", "full_name": "Full Name", "birthday": "Birthday", "birth_place": "Birth Place", "gender": "Gender", "home_town": "Home Town", "current_address": "Current Address", "permanent_address": "Permanent Address", "religion": "Religion", "nation": "Nation", "select_nation": "Select Nation", "education_level": "Education Level", "email": "Email", "phone_number": "Phone Number", "staff_id": "Staff ID", "position": "Position", "department": "Department", "workplace": "Workplace", "workplace_address": "Workplace Address", "start_work_date": "Start Work Date", "identification_number": "Identification Number", "personal_identification": "Personal Identification", "place_of_issue": "Place of Issue", "issue_date": "Issue Date", "personal_tax_code": "Personal Tax Code", "social_insurance_number": "Social Insurance Number", "id_card_images": "ID Card Images", "id_card_front": "Front Side", "id_card_back": "Back Side", "upload_id_front": "Upload front side", "upload_id_back": "Upload back side", "change_image": "Change Image", "max_file_size": "Max 5MB", "file_too_large": "File too large. Please select a file smaller than 5MB", "invalid_file_type": "Invalid file type. Please select an image file", "id_card_front_uploaded": "ID card front side uploaded successfully", "id_card_back_uploaded": "ID card back side uploaded successfully", "id_card_front_removed": "ID card front side removed", "id_card_back_removed": "ID card back side removed", "id_card_note_title": "Notes for ID card photos:", "id_card_note_1": "Image must be clear, not blurry or obscured", "id_card_note_2": "Capture the entire card, no corners cut off", "id_card_note_3": "Format: JPG, PNG. Max size: 5MB", "account_number": "Account Number", "account_name": "Account Name", "bank_name": "Bank Name", "tap_to_change_photo": "Tap to change photo", "email_feedback": "Email cannot be changed", "staff_id_feedback": "Staff ID cannot be changed", "identification_number_feedback": "Identification Number cannot be changed", "select_gender": "Select Gender", "select_education_level": "Select Education Level", "male": "Male", "female": "Female", "other": "Other", "marital_status": "Marital Status", "select_marital_status": "Select Marital Status", "single": "Single", "married": "Married", "divorced": "Divorced", "widowed": "Widowed", "major": "Major", "major_placeholder": "Enter your major", "vehicle_plate": "Vehicle Plate", "vehicle_plate_placeholder": "Enter vehicle plate", "high_school": "High School", "bachelor": "Bachelor's Degree", "master": "Master's Degree", "phd": "PhD", "none": "None", "email_placeholder": "Enter your email", "phone_number_placeholder": "Enter your phone number"}, "scan_id_card": {"title": "Scan ID Card", "extract_info": "Extract Information", "extracting": "Extracting...", "extracted_info": "Extracted Information", "apply_data": "Apply", "missing_images": "Please upload both sides of the ID card", "extract_success": "Information extracted successfully!", "extract_error": "Error extracting information. Please try again.", "data_applied": "Data applied to form"}}}